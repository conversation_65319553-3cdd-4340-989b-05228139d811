<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>isimado.2263</groupId>
        <artifactId>model-function</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>model-function-domain</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter</artifactId>
        </dependency>



        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>






        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java-util</artifactId>
            <version>3.17.1</version>
        </dependency>



        <dependency>
            <groupId>isimado.2263</groupId>
            <artifactId>model-function-share</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>


        <!-- lib -->
        <dependency>
            <groupId>imodel.jsim</groupId>
            <artifactId>jsim-basic-api</artifactId>
            <version>1.5-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../lib/jsim-basic-api-1.5-SNAPSHOT.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>imodel.jsim</groupId>
            <artifactId>jsim-basic-impl</artifactId>
            <version>1.5-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../lib/jsim-basic-impl-1.5-SNAPSHOT.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>imodel.jsim</groupId>
            <artifactId>jsim-core-api</artifactId>
            <version>1.5-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../lib/jsim-core-api-1.5-SNAPSHOT.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>imodel.jsim</groupId>
            <artifactId>jsim-core-impl</artifactId>
            <version>1.5-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../lib/jsim-core-impl-1.5-SNAPSHOT.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>imodel.jsim</groupId>
            <artifactId>jsim-pro-api</artifactId>
            <version>1.5-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../lib/jsim-pro-api-1.5-SNAPSHOT.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>imodel.jsim</groupId>
            <artifactId>jsim-pro-function</artifactId>
            <version>1.5-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../lib/jsim-pro-function-1.5-SNAPSHOT.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>imodel.jsim</groupId>
            <artifactId>jsim-pro-impl</artifactId>
            <version>1.5-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../lib/jsim-pro-impl-1.5-SNAPSHOT.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>imodel.jsim</groupId>
            <artifactId>jsim-processor</artifactId>
            <version>1.5-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../lib/jsim-processor-1.5-SNAPSHOT.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>imodel.jsim</groupId>
            <artifactId>jsim-utils</artifactId>
            <version>1.5-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../lib/jsim-utils-1.5-SNAPSHOT.jar</systemPath>
        </dependency>

        <!-- lib -->

        <dependency>
            <groupId>isim.matrix</groupId>
            <artifactId>engine</artifactId>
            <version>1.2.0-snapshot</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../lib/engine.jar</systemPath>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>isimado.model.function.domain.FunctionApplication</mainClass>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>

                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>